# NCLEX Simulator

Um simulador interativo para o teste NCLEX com geração de questões via IA e sistema adaptativo baseado na Teoria de Resposta ao Item (TRI).

## 🎯 Visão Geral

Esta aplicação web oferece uma experiência de simulação do NCLEX altamente realista e adaptativa, utilizando:

- **Geração Dinâmica de Questões**: Integração com LLMs (OpenAI, OpenRouter) para criar questões de alta qualidade
- **Sistema TRI Adaptativo**: Ajuste automático da dificuldade baseado no desempenho do usuário
- **Feedback Detalhado**: Análise completa do desempenho pós-simulado
- **Tradução Inteligente**: Feature especial de tradução para português brasileiro
- **Interface Moderna**: UI responsiva construída com React e Tailwind CSS

## 🏗️ Arquitetura

### Frontend
- **Framework**: React 19 com Vite
- **UI Components**: Radix UI + shadcn/ui
- **Styling**: Tailwind CSS 4.x
- **Roteamento**: React Router DOM
- **Gráficos**: Recharts
- **Animações**: Framer Motion

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Banco de Dados**: PostgreSQL
- **Autenticação**: JWT
- **Segurança**: Helmet, CORS
- **IA**: Integração com APIs de LLM

## 🚀 Instalação e Execução

### Pré-requisitos
- Node.js 18+
- PostgreSQL
- pnpm (recomendado)

### Backend
```bash
cd backend
npm install
cp .env.example .env
# Configure as variáveis de ambiente
npm run dev
```

### Frontend
```bash
cd frontend/nclex-frontend
pnpm install
pnpm dev
```

## 📁 Estrutura do Projeto

```
nclex-simulator/
├── backend/                 # API Node.js
│   ├── src/                # Código fonte
│   ├── database/           # Scripts de banco
│   └── package.json
├── frontend/               
│   └── nclex-frontend/     # Aplicação React
│       ├── src/
│       ├── public/
│       └── package.json
└── docs/                   # Documentação
    ├── PRD.md             # Requisitos do Produto
    ├── ANALISE_PROFUNDA_PROJETO.md
    └── README_INSTALACAO.md
```

## 🔧 Funcionalidades Principais

### ✅ Implementadas
- Sistema de autenticação de usuários
- Geração de questões via LLM
- Interface de simulado adaptativa
- Sistema TRI para ajuste de dificuldade
- Feedback detalhado pós-simulado
- Tradução de questões (3 cliques)
- Dashboard de progresso
- Histórico de simulados

### 🚧 Em Desenvolvimento
- Melhorias no algoritmo TRI
- Novos tipos de questões
- Sistema de relatórios avançados
- Integração com mais provedores de LLM

## 🎨 Features Especiais

### Tradução Inteligente
- Clique 3x rapidamente em qualquer questão
- Tooltip com tradução para português brasileiro
- Contabilização de traduções utilizadas
- Integração com feedback do simulado

### Sistema TRI Adaptativo
- Ajuste automático da dificuldade
- Terminação inteligente do simulado
- Estimativa precisa da proficiência
- Compatível com padrões NCLEX reais

## 📊 Tecnologias Utilizadas

**Frontend:**
- React 19, Vite, TypeScript
- Tailwind CSS, Radix UI, shadcn/ui
- React Router, React Hook Form
- Recharts, Framer Motion

**Backend:**
- Node.js, Express.js
- PostgreSQL, JWT
- Axios, Helmet, CORS

**DevOps:**
- Git, GitHub
- ESLint, Prettier
- Nodemon (desenvolvimento)


## 📄 Licença

Este projeto está sob a licença ISC. Veja o arquivo `LICENSE` para mais detalhes.

**Nota**: Este é um projeto educacional para simulação do teste NCLEX. Não substitui a preparação oficial para o exame.
