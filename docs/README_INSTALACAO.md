# NCLEX Simulator - Instruções de Instalação e Uso

## 📋 Visão Geral

O NCLEX Simulator é uma aplicação web completa para simulados adaptativos do exame NCLEX, desenvolvida com React (frontend) e Node.js (backend), utilizando algoritmo TRI (Teoria de Resposta ao Item) e geração de questões via IA.

## 🚀 Funcionalidades Principais

- ✅ **Sistema de Autenticação** completo (login/cadastro)
- ✅ **Algoritmo TRI Adaptativo** para ajuste de dificuldade
- ✅ **Geração de Questões via IA** (OpenAI GPT)
- ✅ **Tradução com Clique Triplo** (português/inglês)
- ✅ **Dashboard de Progresso** com análises detalhadas
- ✅ **Relatórios TRI** com estimativa de habilidade
- ✅ **Interface Moderna** e responsiva
- ✅ **Simulados Simplificados** (5-10 questões para testes)

## 📁 Estrutura do Projeto

```
nclex-simulator/
├── frontend/nclex-frontend/    # Aplicação React
├── backend/                    # API Node.js
├── docs/                      # Documentação
└── database/migrations/       # Scripts SQL
```

## 🛠️ Pré-requisitos

- **Node.js** 18+ 
- **npm** ou **yarn**
- **PostgreSQL** 12+
- **Chave OpenAI API** (opcional - usa mock service por padrão)

## 📦 Instalação

### 1. Backend (API)

```bash
cd nclex-simulator/backend

# Instalar dependências
npm install

# Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com suas configurações

# Configurar banco de dados PostgreSQL
# Executar migrations em database/migrations/

# Iniciar servidor
npm start
```

### 2. Frontend (React)

```bash
cd nclex-simulator/frontend/nclex-frontend

# Instalar dependências
npm install

# Configurar URL da API em src/services/api.js
# Alterar API_BASE_URL para seu backend

# Iniciar aplicação
npm run dev
```

## ⚙️ Configuração

### Backend (.env)

```env
# Banco de dados
DB_HOST=localhost
DB_PORT=5432
DB_NAME=nclex_simulator
DB_USER=seu_usuario
DB_PASSWORD=sua_senha

# JWT
JWT_SECRET=sua_chave_secreta_jwt

# OpenAI (opcional)
OPENAI_API_KEY=sua_chave_openai

# Servidor
PORT=5000
NODE_ENV=development
```

### Frontend (api.js)

```javascript
const API_BASE_URL = 'http://localhost:5000/api';
```

## 🗄️ Banco de Dados

Execute as migrations na ordem:

1. `001_create_users_table.sql`
2. `002_create_simulations_table.sql`

```sql
-- Exemplo de criação do banco
CREATE DATABASE nclex_simulator;
```

## 🚀 Deploy

### Frontend
- Build: `npm run build`
- Deploy pasta `dist/` em servidor web

### Backend
- Configurar variáveis de ambiente de produção
- Deploy em servidor Node.js (PM2, Docker, etc.)

## 🧪 Modo de Teste

O sistema inclui um **Mock Service** com 8 questões variadas para testes sem necessidade de API OpenAI:

- Questões de enfermagem realistas
- Diferentes áreas de conteúdo
- Níveis de dificuldade variados
- Sistema anti-repetição implementado

## 📊 Funcionalidades Técnicas

### Algoritmo TRI
- Estimativa de habilidade em tempo real
- Seleção adaptativa de dificuldade
- Critérios de terminação configuráveis
- Relatórios estatísticos detalhados

### Sistema de Questões
- Geração via OpenAI GPT-4
- Fallback para mock service
- Controle de questões já utilizadas
- Múltiplas áreas de conteúdo

### Interface
- Design responsivo (mobile/desktop)
- Componentes reutilizáveis (shadcn/ui)
- Tradução instantânea
- Feedback visual em tempo real

## 🔧 Solução de Problemas

### Erro de CORS
- Verificar configuração de origens permitidas no backend
- Atualizar URL do frontend na lista CORS

### Loop de Questões
- ✅ **Resolvido** - Sistema anti-repetição implementado
- Mock service expandido com 8 questões variadas

### Conectividade
- Verificar se backend está rodando na porta correta
- Confirmar URL da API no frontend

## 📈 Melhorias Futuras

- Integração com mais provedores de IA
- Banco de questões expandido
- Sistema de relatórios avançado
- Modo offline
- Análise de performance detalhada

## 🏆 Status do Projeto

**✅ TOTALMENTE FUNCIONAL**
- Todos os bugs críticos resolvidos
- Sistema de simulados operacional
- Interface completa e responsiva
- Pronto para uso em produção

## 📞 Suporte

Para dúvidas ou problemas:
1. Verificar logs do backend/frontend
2. Consultar documentação técnica
3. Revisar configurações de ambiente

---

**Desenvolvido com ❤️ para educação em enfermagem**

