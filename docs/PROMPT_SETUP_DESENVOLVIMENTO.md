# 🚀 PROMPT PARA CONFIGURAÇÃO DO AMBIENTE DE DESENVOLVIMENTO - NCLEX SIMULATOR

## 📋 CONTEXTO DO PROJETO

Você está configurando um **simulador NCLEX completo** com as seguintes características:

- **Frontend**: React 19.1.0 + Vite + Tailwind CSS 4.1.7
- **Backend**: Node.js + Express 5.1.0 + PostgreSQL
- **Funcionalidades**: Sistema TRI adaptativo, geração de questões via LLM, tradução com clique triplo
- **Status**: Projeto completo e funcional, precisa ser configurado em localhost

---

## 🎯 OBJETIVO

Configure e execute a aplicação NCLEX Simulator em ambiente de desenvolvimento local, seguindo estas etapas:

### **ETAPA 1: VERIFICAÇÃO DE PRÉ-REQUISITOS**

Verifique se estão instalados:
- **Node.js 18+** (`node --version`)
- **npm ou yarn** (`npm --version`)
- **PostgreSQL 12+** (`psql --version`)
- **Git** (`git --version`)

Se algum estiver faltando, instale antes de prosseguir.

### **ETAPA 2: CONFIGURAÇÃO DO BANCO DE DADOS**

1. **Inicie o PostgreSQL** (se não estiver rodando)
2. **Crie o banco de dados**:
   ```sql
   CREATE DATABASE nclex_simulator;
   CREATE USER nclex_user WITH PASSWORD 'nclex_password';
   GRANT ALL PRIVILEGES ON DATABASE nclex_simulator TO nclex_user;
   ```

3. **Execute as migrations** na ordem:
   - Navegue para `backend/database/migrations/`
   - Execute cada arquivo SQL na ordem numérica
   - Verifique se as tabelas foram criadas corretamente

### **ETAPA 3: CONFIGURAÇÃO DO BACKEND**

1. **Navegue para a pasta backend**:
   ```bash
   cd backend
   ```

2. **Instale as dependências**:
   ```bash
   npm install
   ```

3. **Configure as variáveis de ambiente**:
   - Crie arquivo `.env` na raiz do backend
   - Configure as seguintes variáveis:
   ```env
   # Banco de dados
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=nclex_simulator
   DB_USER=nclex_user
   DB_PASSWORD=nclex_password

   # JWT
   JWT_SECRET=sua_chave_secreta_jwt_muito_segura_aqui

   # OpenAI (opcional - funciona com mock se não configurar)
   OPENAI_API_KEY=sua_chave_openai_aqui

   # Servidor
   PORT=5000
   NODE_ENV=development
   ```

4. **Teste a conexão com o banco**:
   ```bash
   npm start
   ```
   - Deve aparecer: "🚀 Servidor rodando na porta 5000"
   - Acesse: http://localhost:5000/api/health
   - Deve retornar JSON com status "OK"

### **ETAPA 4: CONFIGURAÇÃO DO FRONTEND**

1. **Abra um novo terminal** e navegue para o frontend:
   ```bash
   cd frontend/nclex-frontend
   ```

2. **Instale as dependências**:
   ```bash
   npm install
   ```

3. **Configure a URL da API**:
   - Abra `src/services/api.js`
   - Verifique se `API_BASE_URL` está como: `'http://localhost:5000/api'`

4. **Inicie o servidor de desenvolvimento**:
   ```bash
   npm run dev
   ```
   - Deve aparecer: "Local: http://localhost:5173"
   - Acesse no navegador

### **ETAPA 5: TESTE COMPLETO DA APLICAÇÃO**

1. **Acesse http://localhost:5173**
2. **Teste o registro de usuário**:
   - Clique em "Cadastrar"
   - Preencha os dados
   - Verifique se o usuário foi criado no banco

3. **Teste o login**:
   - Faça login com o usuário criado
   - Deve redirecionar para o dashboard

4. **Teste um simulado**:
   - Clique em "Iniciar Simulado"
   - Responda algumas questões
   - Teste a feature de tradução (clique triplo na questão)
   - Complete o simulado e veja os resultados

5. **Teste o dashboard**:
   - Verifique se as estatísticas aparecem
   - Teste a navegação entre seções

### **ETAPA 6: VERIFICAÇÃO DE FUNCIONALIDADES ESPECÍFICAS**

**Sistema TRI**:
- ✅ Simulado deve adaptar dificuldade baseado nas respostas
- ✅ Deve terminar entre 5-10 questões (configuração de teste)
- ✅ Relatório TRI deve aparecer no final

**Feature de Tradução**:
- ✅ Clique triplo na questão deve mostrar tooltip com tradução
- ✅ Contador de traduções deve aparecer no resultado
- ✅ Interface visual deve ser elegante

**Geração de Questões**:
- ✅ Questões devem ser geradas dinamicamente
- ✅ Justificativas devem aparecer após resposta
- ✅ Diferentes níveis de dificuldade devem ser apresentados

### **ETAPA 7: TROUBLESHOOTING COMUM**

**Se o backend não conectar ao banco**:
- Verifique se PostgreSQL está rodando
- Confirme credenciais no .env
- Teste conexão manual: `psql -h localhost -U nclex_user -d nclex_simulator`

**Se o frontend não carregar**:
- Verifique se backend está rodando na porta 5000
- Confirme URL da API em `src/services/api.js`
- Verifique console do navegador para erros

**Se as questões não gerarem**:
- Sem OpenAI API Key: deve usar mock service automaticamente
- Com OpenAI API Key: verifique se a chave está válida
- Verifique logs do backend para erros específicos

**Se a tradução não funcionar**:
- Teste clique triplo rápido na questão
- Verifique se tooltip aparece
- Sem OpenAI API Key: deve mostrar tradução mock

---

## 🎯 RESULTADO ESPERADO

Após seguir todos os passos, você deve ter:

✅ **Backend rodando** em http://localhost:5000  
✅ **Frontend rodando** em http://localhost:5173  
✅ **Banco PostgreSQL** configurado e conectado  
✅ **Sistema de autenticação** funcionando  
✅ **Simulados TRI** executando corretamente  
✅ **Feature de tradução** ativa  
✅ **Dashboard** com estatísticas  
✅ **Todas as funcionalidades** testadas  

---

## 📞 SUPORTE

Se encontrar problemas:

1. **Verifique os logs** do backend e frontend
2. **Confirme todas as dependências** estão instaladas
3. **Teste cada etapa** individualmente
4. **Verifique as portas** 5000 e 5173 estão livres
5. **Confirme as variáveis de ambiente** estão corretas

---

## 🚀 PRÓXIMOS PASSOS APÓS CONFIGURAÇÃO

Uma vez funcionando, você pode:

- **Explorar o código** para entender a implementação
- **Testar diferentes cenários** de uso
- **Modificar configurações** TRI para diferentes comportamentos
- **Adicionar novas funcionalidades** seguindo a arquitetura existente
- **Configurar OpenAI API Key** para questões reais (opcional)

---

**IMPORTANTE**: Este é um projeto completo e profissional. Siga cada etapa cuidadosamente para garantir que todas as funcionalidades funcionem corretamente em seu ambiente local.

**Data**: 13 de Janeiro de 2025  
**Versão**: 1.0
