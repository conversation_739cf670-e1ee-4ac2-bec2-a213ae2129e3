import React, { useState, useEffect } from 'react';
import apiService from '../services/api';

const DashboardPage = ({ onNavigateToSimulation }) => {
  const [performanceData, setPerformanceData] = useState(null);
  const [historyData, setHistoryData] = useState(null);
  const [contentAreasData, setContentAreasData] = useState(null);
  const [insightsData, setInsightsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carregar dados em paralelo
      const [performance, history, contentAreas, insights] = await Promise.allSettled([
        apiService.getUserPerformanceAnalysis(),
        apiService.getUserSimulationHistory(5, 0),
        apiService.getContentAreaStatistics(),
        apiService.getPersonalizedInsights()
      ]);

      // Processar resultados
      if (performance.status === 'fulfilled') {
        setPerformanceData(performance.value.data);
      }
      
      if (history.status === 'fulfilled') {
        setHistoryData(history.value.data);
      }
      
      if (contentAreas.status === 'fulfilled') {
        setContentAreasData(contentAreas.value.data);
      }
      
      if (insights.status === 'fulfilled') {
        setInsightsData(insights.value.data);
      }

    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceColor = (value, type = 'accuracy') => {
    if (type === 'accuracy') {
      if (value >= 80) return 'text-green-600';
      if (value >= 65) return 'text-blue-600';
      if (value >= 50) return 'text-yellow-600';
      return 'text-red-600';
    }
    
    if (type === 'ability') {
      if (value >= 1) return 'text-green-600';
      if (value >= 0) return 'text-blue-600';
      if (value >= -1) return 'text-yellow-600';
      return 'text-red-600';
    }
    
    return 'text-gray-600';
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'improving':
        return (
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'declining':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      case 'stable':
        return (
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getStrengthColor = (level) => {
    switch (level) {
      case 'strong': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-blue-100 text-blue-800';
      case 'weak': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Erro ao carregar dashboard</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadDashboardData}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard de Progresso</h1>
          <p className="text-gray-600">Acompanhe seu desempenho e evolução nos simulados NCLEX</p>
        </div>

        {/* Estatísticas Gerais */}
        {performanceData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Simulados Realizados</p>
                  <p className="text-2xl font-bold text-gray-900">{performanceData.totalSimulations}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Precisão Média</p>
                  <p className={`text-2xl font-bold ${getPerformanceColor(performanceData.averageAccuracy)}`}>
                    {performanceData.averageAccuracy.toFixed(1)}%
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Habilidade Estimada</p>
                  <p className={`text-2xl font-bold ${getPerformanceColor(performanceData.averageAbility, 'ability')}`}>
                    {performanceData.averageAbility.toFixed(2)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Tendência</p>
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(performanceData.improvementTrend)}
                    <span className="text-sm font-medium text-gray-900 capitalize">
                      {performanceData.improvementTrend === 'improving' ? 'Melhorando' :
                       performanceData.improvementTrend === 'declining' ? 'Declinando' :
                       performanceData.improvementTrend === 'stable' ? 'Estável' : 'Insuficiente'}
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Histórico Recente */}
          {historyData && historyData.simulations.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Simulados Recentes</h3>
              <div className="space-y-4">
                {historyData.simulations.map((simulation) => (
                  <div key={simulation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          simulation.status === 'completed' ? 'bg-green-500' : 
                          simulation.status === 'in_progress' ? 'bg-yellow-500' : 'bg-gray-500'
                        }`}></div>
                        <span className="text-sm text-gray-600">
                          {new Date(simulation.createdAt).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                      <div className="mt-1">
                        <span className="text-sm font-medium text-gray-900">
                          {simulation.totalQuestions} questões
                        </span>
                        {simulation.status === 'completed' && (
                          <span className={`ml-2 text-sm ${getPerformanceColor(simulation.accuracy)}`}>
                            {simulation.accuracy.toFixed(1)}% de acertos
                          </span>
                        )}
                      </div>
                    </div>
                    {simulation.status === 'completed' && simulation.durationMinutes && (
                      <div className="text-right">
                        <span className="text-sm text-gray-600">
                          {Math.round(simulation.durationMinutes)} min
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Áreas de Conteúdo */}
          {contentAreasData && contentAreasData.contentAreas.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance por Área</h3>
              <div className="space-y-3">
                {contentAreasData.contentAreas.slice(0, 5).map((area) => (
                  <div key={area.contentArea} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900">{area.contentArea}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStrengthColor(area.strengthLevel)}`}>
                          {area.strengthLevel === 'strong' ? 'Forte' :
                           area.strengthLevel === 'moderate' ? 'Moderado' :
                           area.strengthLevel === 'weak' ? 'Fraco' : 'Crítico'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              area.accuracy >= 80 ? 'bg-green-500' :
                              area.accuracy >= 65 ? 'bg-blue-500' :
                              area.accuracy >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${Math.min(area.accuracy, 100)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-12 text-right">
                          {area.accuracy.toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Recomendações */}
        {performanceData && performanceData.recommendations.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recomendações Personalizadas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {performanceData.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
                  <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm text-blue-900">{recommendation}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Ações Rápidas */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={onNavigateToSimulation}
              className="flex items-center justify-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>Novo Simulado</span>
            </button>

            <button
              onClick={loadDashboardData}
              className="flex items-center justify-center space-x-2 bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Atualizar Dados</span>
            </button>

            <button className="flex items-center justify-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span>Ver Relatórios</span>
            </button>
          </div>
        </div>

        {/* Mensagem para usuários sem dados */}
        {performanceData && performanceData.totalSimulations === 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <svg className="w-12 h-12 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Bem-vindo ao NCLEX Simulator!</h3>
            <p className="text-blue-800 mb-4">
              Você ainda não realizou nenhum simulado. Comece agora para ver suas análises e progresso aqui.
            </p>
            <button
              onClick={onNavigateToSimulation}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Fazer Primeiro Simulado
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;

