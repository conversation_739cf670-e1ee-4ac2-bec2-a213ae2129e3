import React, { useState } from 'react';
import LoginForm from '../components/auth/LoginForm';
import RegisterForm from '../components/auth/RegisterForm';

const AuthPage = ({ onAuthSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);

  const toggleMode = () => {
    setIsLogin(!isLogin);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 items-center">
        {/* Lado esquerdo - Informações */}
        <div className="hidden lg:block">
          <div className="text-center lg:text-left">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              NCLEX Simulator
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Prepare-se para o exame NCLEX com simulados adaptativos baseados em 
              Teoria de Resposta ao Item (TRI), questões geradas por IA e feedback detalhado.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Simulados Adaptativos
                  </h3>
                  <p className="text-gray-600">
                    Algoritmo TRI que adapta a dificuldade das questões baseado no seu desempenho, 
                    simulando o exame real do NCLEX.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Questões Geradas por IA
                  </h3>
                  <p className="text-gray-600">
                    Questões clinicamente precisas geradas por inteligência artificial, 
                    com justificativas detalhadas e múltiplos formatos.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Análise Detalhada
                  </h3>
                  <p className="text-gray-600">
                    Relatórios completos de desempenho com estimativa de habilidade, 
                    áreas de melhoria e recomendações personalizadas.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Lado direito - Formulário */}
        <div className="w-full">
          {isLogin ? (
            <LoginForm 
              onToggleMode={toggleMode} 
              onSuccess={onAuthSuccess}
            />
          ) : (
            <RegisterForm 
              onToggleMode={toggleMode} 
              onSuccess={onAuthSuccess}
            />
          )}
        </div>
      </div>

      {/* Informações móveis */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-900 mb-1">
            NCLEX Simulator
          </h2>
          <p className="text-sm text-gray-600">
            Simulados adaptativos com IA para sua preparação
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;

