import React, { useState, useEffect } from 'react';
import SimulationStart from '../components/simulation/SimulationStart';
import QuestionDisplay from '../components/simulation/QuestionDisplay';
import apiService from '../services/api';

const SimulationPage = ({ onBackToDashboard }) => {
  const [simulationState, setSimulationState] = useState('start'); // 'start', 'question', 'completed'
  const [simulationData, setSimulationData] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSimulationStarted = async (startResponse) => {
    try {
      setLoading(true);
      setSimulationData(startResponse);
      
      // Buscar primeira questão
      const questionResponse = await apiService.getNextQuestion(startResponse.simulationId);
      setCurrentQuestion(questionResponse);
      setSimulationState('question');
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerSubmitted = async (answerResponse) => {
    try {
      setLoading(true);
      
      if (answerResponse.isSimulationComplete) {
        // Simulado concluído
        setSimulationState('completed');
        setSimulationData(prev => ({
          ...prev,
          triReport: answerResponse.triReport
        }));
      } else {
        // Buscar próxima questão
        const questionResponse = await apiService.getNextQuestion(simulationData.simulationId);
        setCurrentQuestion(questionResponse);
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderSimulationCompleted = () => {
    const report = simulationData?.triReport;
    if (!report) return null;

    const getResultColor = (result) => {
      if (result === 'pass') return 'text-green-600';
      if (result === 'fail') return 'text-red-600';
      return 'text-gray-600';
    };

    const getResultBg = (result) => {
      if (result === 'pass') return 'bg-green-50 border-green-200';
      if (result === 'fail') return 'bg-red-50 border-red-200';
      return 'bg-gray-50 border-gray-200';
    };

    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
              report.overview.result === 'pass' ? 'bg-green-100' : 'bg-red-100'
            }`}>
              {report.overview.result === 'pass' ? (
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Simulado Concluído!
            </h2>
            <p className={`text-xl font-semibold ${getResultColor(report.overview.result)}`}>
              {report.overview.result === 'pass' ? 'Aprovado' : 
               report.overview.result === 'fail' ? 'Reprovado' : 'Resultado Indefinido'}
            </p>
          </div>

          {/* Resumo geral */}
          <div className={`rounded-lg border-2 p-6 mb-8 ${getResultBg(report.overview.result)}`}>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo do Desempenho</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{report.overview.totalQuestions}</div>
                <div className="text-sm text-gray-600">Questões</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{report.overview.correctAnswers}</div>
                <div className="text-sm text-gray-600">Acertos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{report.overview.accuracy}%</div>
                <div className="text-sm text-gray-600">Precisão</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{report.overview.confidence}%</div>
                <div className="text-sm text-gray-600">Confiança</div>
              </div>
            </div>
          </div>

          {/* Análise de habilidade */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Análise de Habilidade</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-blue-800">Habilidade Estimada:</span>
                  <span className="font-semibold text-blue-900">{report.ability.estimated}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-800">Percentil:</span>
                  <span className="font-semibold text-blue-900">{report.ability.percentile}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-800">Erro Padrão:</span>
                  <span className="font-semibold text-blue-900">{report.ability.standardError}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-800">Prob. Aprovação:</span>
                  <span className="font-semibold text-blue-900">{report.ability.passProbability}%</span>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-purple-900 mb-4">Interpretação</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-purple-800 font-medium">Nível:</span>
                  <span className="ml-2 text-purple-900">{report.interpretation.level}</span>
                </div>
                <p className="text-purple-800 text-sm">{report.interpretation.description}</p>
              </div>
            </div>
          </div>

          {/* Recomendações */}
          <div className="bg-yellow-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-yellow-900 mb-4">Recomendações para Estudo</h3>
            <ul className="space-y-2">
              {report.interpretation.recommendations.map((rec, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-yellow-800">{rec}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Botões de ação */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => {
                setSimulationState('start');
                setSimulationData(null);
                setCurrentQuestion(null);
                setError(null);
              }}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Novo Simulado
            </button>
            <button
              onClick={onBackToDashboard}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
            >
              Voltar ao Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Erro no Simulado</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => {
                setError(null);
                setSimulationState('start');
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={onBackToDashboard}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Voltar ao Dashboard
          </button>
          <h1 className="text-3xl font-bold text-gray-900">
            {simulationState === 'start' && 'Simulado NCLEX'}
            {simulationState === 'question' && 'Simulado em Andamento'}
            {simulationState === 'completed' && 'Resultado do Simulado'}
          </h1>
        </div>

        {/* Conteúdo baseado no estado */}
        {simulationState === 'start' && (
          <SimulationStart onSimulationStarted={handleSimulationStarted} />
        )}

        {simulationState === 'question' && currentQuestion && (
          <QuestionDisplay
            simulationId={simulationData.simulationId}
            question={currentQuestion.question}
            questionNumber={currentQuestion.questionNumber}
            progress={currentQuestion.progress}
            triState={currentQuestion.triState}
            onAnswerSubmitted={handleAnswerSubmitted}
          />
        )}

        {simulationState === 'completed' && renderSimulationCompleted()}
      </div>
    </div>
  );
};

export default SimulationPage;

