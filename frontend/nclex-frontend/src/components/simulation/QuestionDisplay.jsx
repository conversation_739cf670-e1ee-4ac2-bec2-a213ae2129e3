import React, { useState, useEffect } from 'react';
import apiService from '../../services/api';

const QuestionDisplay = ({ 
  simulationId, 
  question, 
  questionNumber, 
  progress, 
  triState, 
  onAnswerSubmitted 
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState([]);
  const [timeSpent, setTimeSpent] = useState(0);
  const [startTime] = useState(Date.now());
  const [loading, setLoading] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  const [translation, setTranslation] = useState(null);
  const [clickCount, setClickCount] = useState(0);
  const [clickTimer, setClickTimer] = useState(null);

  // Atualizar tempo gasto a cada segundo
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  // Lidar com clique triplo para tradução
  const handleQuestionClick = async () => {
    setClickCount(prev => prev + 1);

    // Limpar timer anterior
    if (clickTimer) {
      clearTimeout(clickTimer);
    }

    // Definir novo timer para resetar contagem
    const newTimer = setTimeout(() => {
      setClickCount(0);
    }, 500); // 500ms para detectar clique triplo

    setClickTimer(newTimer);

    // Se foi o terceiro clique
    if (clickCount === 2) {
      setClickCount(0);
      clearTimeout(newTimer);
      
      if (!translation) {
        try {
          const response = await apiService.translateQuestion(question.text);
          setTranslation(response.translation);
        } catch (error) {
          console.error('Erro ao traduzir questão:', error);
        }
      }
      setShowTranslation(!showTranslation);
    }
  };

  const handleAnswerChange = (optionId) => {
    if (question.type === 'multiple_choice') {
      setSelectedAnswer([optionId]);
    } else if (question.type === 'multiple_response') {
      setSelectedAnswer(prev => {
        if (prev.includes(optionId)) {
          return prev.filter(id => id !== optionId);
        } else {
          return [...prev, optionId];
        }
      });
    }
  };

  const submitAnswer = async () => {
    if (selectedAnswer.length === 0) {
      alert('Por favor, selecione uma resposta antes de continuar.');
      return;
    }

    try {
      setLoading(true);
      
      const response = await apiService.submitAnswer(
        simulationId,
        question.id,
        selectedAnswer,
        timeSpent
      );

      if (onAnswerSubmitted) {
        onAnswerSubmitted(response);
      }
    } catch (error) {
      console.error('Erro ao submeter resposta:', error);
      alert('Erro ao submeter resposta. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    if (!progress) return 0;
    return Math.min((questionNumber / progress.min) * 100, 100);
  };

  const getAbilityColor = (ability) => {
    if (ability > 1) return 'text-green-600';
    if (ability > 0) return 'text-blue-600';
    if (ability > -1) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header com progresso */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Questão {questionNumber}
            </h2>
            <p className="text-sm text-gray-600">
              {progress && `Mínimo: ${progress.min} questões • Máximo: ${progress.max} questões`}
            </p>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-900">
              {formatTime(timeSpent)}
            </div>
            <div className="text-sm text-gray-600">
              Tempo gasto
            </div>
          </div>
        </div>

        {/* Barra de progresso */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>

        {/* Estatísticas TRI */}
        {triState && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {triState.accuracy}%
              </div>
              <div className="text-sm text-gray-600">Acertos</div>
            </div>
            <div>
              <div className={`text-lg font-semibold ${getAbilityColor(triState.currentAbility)}`}>
                {triState.currentAbility.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Habilidade</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {triState.confidence}%
              </div>
              <div className="text-sm text-gray-600">Confiança</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {triState.questionCount}
              </div>
              <div className="text-sm text-gray-600">Respondidas</div>
            </div>
          </div>
        )}
      </div>

      {/* Questão */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <div className="mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div 
                className="text-lg leading-relaxed text-gray-900 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
                onClick={handleQuestionClick}
                title="Clique 3 vezes para traduzir"
              >
                {question.text}
              </div>
              
              {/* Tradução */}
              {showTranslation && translation && (
                <div className="mt-4 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
                  <div className="flex items-center mb-2">
                    <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                    <span className="text-sm font-medium text-blue-800">Tradução:</span>
                  </div>
                  <p className="text-blue-900">{translation}</p>
                </div>
              )}
            </div>
            
            <div className="ml-4 text-sm text-gray-500">
              <div className="bg-gray-100 px-3 py-1 rounded">
                {question.contentArea}
              </div>
            </div>
          </div>
        </div>

        {/* Opções de resposta */}
        <div className="space-y-3 mb-8">
          {question.options.map((option) => (
            <label
              key={option.id}
              className={`flex items-start p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                selectedAnswer.includes(option.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <input
                type={question.type === 'multiple_choice' ? 'radio' : 'checkbox'}
                name="answer"
                value={option.id}
                checked={selectedAnswer.includes(option.id)}
                onChange={() => handleAnswerChange(option.id)}
                className="mt-1 mr-3"
              />
              <div className="flex-1">
                <div className="font-medium text-gray-900 mb-1">
                  {option.id}. {option.text}
                </div>
              </div>
            </label>
          ))}
        </div>

        {/* Instruções */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="text-sm text-gray-600">
              <p className="mb-1">
                <strong>Dica:</strong> Clique 3 vezes rapidamente na questão para ver a tradução em português.
              </p>
              {question.type === 'multiple_response' && (
                <p>
                  <strong>Atenção:</strong> Esta questão permite múltiplas respostas. Selecione todas as opções corretas.
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Botão de submissão */}
        <div className="text-center">
          <button
            onClick={submitAnswer}
            disabled={loading || selectedAnswer.length === 0}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Enviando...
              </div>
            ) : (
              'Confirmar Resposta'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuestionDisplay;

