{"name": "nclex-simulator-backend", "version": "1.0.0", "description": "Backend para simulador NCLEX com geração de questões via LLM e sistema TRI adaptativo", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nclex", "simulator", "llm", "tri", "adaptive"], "author": "Manus AI", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "pg": "^8.16.3"}, "devDependencies": {"nodemon": "^3.1.10"}}