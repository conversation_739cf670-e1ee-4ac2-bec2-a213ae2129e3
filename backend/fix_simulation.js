// Script para limpar simulados em andamento
const { query } = require('./src/config/database');

async function fixSimulations() {
  try {
    console.log('🔧 Limpando simulados em andamento...');
    
    // Atualizar todos os simulados em andamento para completados
    const result = await query(
      `UPDATE simulations 
       SET status = 'completed', completed_at = CURRENT_TIMESTAMP 
       WHERE status = 'in_progress'`
    );
    
    console.log(`✅ ${result.rowCount} simulados atualizados para 'completed'`);
    
    // Verificar se há simulados restantes
    const check = await query(
      'SELECT COUNT(*) as count FROM simulations WHERE status = \'in_progress\''
    );
    
    console.log(`📊 Simulados em andamento restantes: ${check.rows[0].count}`);
    
    console.log('🎉 Limpeza concluída! Agora você pode iniciar novos simulados.');
    
  } catch (error) {
    console.error('❌ Erro ao limpar simulados:', error);
  }
  
  process.exit(0);
}

fixSimulations();

