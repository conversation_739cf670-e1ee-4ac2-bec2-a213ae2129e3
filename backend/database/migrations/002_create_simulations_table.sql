-- Migração 002: <PERSON><PERSON><PERSON> tabelas de simulados e questões
-- Data: 2024-12-08
-- Descrição: Tabelas para armazenar simulados, questões e respostas dos usuários

-- Tabela de simulados
CREATE TABLE IF NOT EXISTS simulations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'abandoned')),
    total_questions INTEGER DEFAULT 0,
    correct_answers INTEGER DEFAULT 0,
    final_score DECIMAL(5,2),
    estimated_ability DECIMAL(5,2), -- Para o algoritmo TRI
    confidence_level DECIMAL(5,2), -- <PERSON><PERSON>vel de confian<PERSON> do TRI
    translations_used INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de questões do simulado
CREATE TABLE IF NOT EXISTS simulation_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    simulation_id UUID NOT NULL REFERENCES simulations(id) ON DELETE CASCADE,
    question_number INTEGER NOT NULL,
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL, -- 'multiple_choice', 'multiple_response', 'fill_blank', etc.
    options JSONB, -- Opções de resposta em formato JSON
    correct_answer JSONB NOT NULL, -- Resposta correta em formato JSON
    user_answer JSONB, -- Resposta do usuário em formato JSON
    is_correct BOOLEAN,
    difficulty_level DECIMAL(5,2), -- Nível de dificuldade TRI
    content_area VARCHAR(100), -- Área de conhecimento (ex: 'Safe and Effective Care Environment')
    rationale TEXT, -- Justificativa da resposta
    time_spent INTEGER, -- Tempo gasto na questão em segundos
    was_translated BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    answered_at TIMESTAMP WITH TIME ZONE
);

-- Índices para otimização
CREATE INDEX IF NOT EXISTS idx_simulations_user_id ON simulations(user_id);
CREATE INDEX IF NOT EXISTS idx_simulations_status ON simulations(status);
CREATE INDEX IF NOT EXISTS idx_simulations_created_at ON simulations(created_at);

CREATE INDEX IF NOT EXISTS idx_simulation_questions_simulation_id ON simulation_questions(simulation_id);
CREATE INDEX IF NOT EXISTS idx_simulation_questions_question_number ON simulation_questions(question_number);
CREATE INDEX IF NOT EXISTS idx_simulation_questions_content_area ON simulation_questions(content_area);

-- Triggers para atualizar updated_at
CREATE TRIGGER update_simulations_updated_at 
    BEFORE UPDATE ON simulations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

