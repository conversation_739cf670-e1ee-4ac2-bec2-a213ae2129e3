const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { testConnection } = require('./config/database');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware de segurança
app.use(helmet());

// Middleware de logging
app.use(morgan('combined'));

// Middleware CORS
const allowedOrigins = [
  'http://localhost:3000',
  'https://wtufepvy.manus.space',
  'https://xknlkhuk.manus.space',
  'https://xcwsenle.manus.space',
  'https://nhspmphs.manus.space',
  'https://khsusked.manus.space',
  'https://rtpgylwe.manus.space',
  process.env.CORS_ORIGIN
].filter(Boolean);

app.use(cors({
  origin: function (origin, callback) {
    // Permitir requisições sem origin (ex: mobile apps, Postman)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true
}));

// Middleware para parsing JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Testar conexão com banco de dados na inicialização
testConnection();

// Importar rotas
const authRoutes = require('./routes/auth');
const questionRoutes = require('./routes/questions');
const simulationRoutes = require('./routes/simulations');
const analysisRoutes = require('./routes/analysis');

// Usar rotas
app.use('/api/auth', authRoutes);
app.use('/api/questions', questionRoutes);
app.use('/api/simulations', simulationRoutes);
app.use('/api/analysis', analysisRoutes);

// Rota de teste
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'NCLEX Simulator API está funcionando',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    database: 'Connected'
  });
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Algo deu errado!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Erro interno do servidor'
  });
});

// Middleware para rotas não encontradas
app.use((req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    message: `A rota ${req.originalUrl} não existe`
  });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌍 Ambiente: ${process.env.NODE_ENV}`);
  console.log(`📊 API Health Check: http://localhost:${PORT}/api/health`);
});

module.exports = app;

