const llmService = require('../services/llmService');
const { query } = require('../config/database');

// Áreas de conteúdo do NCLEX
const CONTENT_AREAS = [
  'Safe and Effective Care Environment',
  'Health Promotion and Maintenance', 
  'Psychosocial Integrity',
  'Physiological Integrity'
];

// Tipos de questões suportados
const QUESTION_TYPES = [
  'multiple_choice',
  'multiple_response',
  'fill_blank',
  'drag_drop'
];

// Níveis de dificuldade
const DIFFICULTY_LEVELS = ['easy', 'medium', 'hard'];

// Gerar uma nova questão
const generateQuestion = async (req, res) => {
  try {
    const {
      difficulty = 'medium',
      contentArea = 'Safe and Effective Care Environment',
      questionType = 'multiple_choice',
      simulationId = null
    } = req.body;

    // Validações
    if (!DIFFICULTY_LEVELS.includes(difficulty)) {
      return res.status(400).json({
        error: 'Nível de dificuldade inválido',
        message: `Níveis válidos: ${DIFFICULTY_LEVELS.join(', ')}`
      });
    }

    if (!CONTENT_AREAS.includes(contentArea)) {
      return res.status(400).json({
        error: 'Área de conteúdo inválida',
        message: `Áreas válidas: ${CONTENT_AREAS.join(', ')}`
      });
    }

    if (!QUESTION_TYPES.includes(questionType)) {
      return res.status(400).json({
        error: 'Tipo de questão inválido',
        message: `Tipos válidos: ${QUESTION_TYPES.join(', ')}`
      });
    }

    // Buscar questões anteriores para evitar repetição (se for parte de um simulado)
    let previousQuestions = [];
    if (simulationId) {
      const prevResult = await query(
        'SELECT question_text FROM simulation_questions WHERE simulation_id = $1 ORDER BY question_number',
        [simulationId]
      );
      previousQuestions = prevResult.rows.map(row => row.question_text);
    }

    // Gerar questão via LLM
    const questionData = await llmService.generateQuestion({
      difficulty,
      contentArea,
      questionType,
      previousQuestions
    });

    // Calcular nível de dificuldade numérico para TRI
    const difficultyValue = getDifficultyValue(difficulty);

    // Preparar resposta
    const response = {
      ...questionData,
      difficulty_value: difficultyValue,
      generated_at: new Date().toISOString()
    };

    res.json({
      message: 'Questão gerada com sucesso',
      question: response
    });

  } catch (error) {
    console.error('Erro ao gerar questão:', error);
    
    if (error.message.includes('LLM')) {
      return res.status(503).json({
        error: 'Serviço de geração indisponível',
        message: 'Não foi possível gerar a questão no momento. Tente novamente.'
      });
    }

    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao gerar questão'
    });
  }
};

// Salvar questão no banco de dados (para simulados)
const saveQuestion = async (req, res) => {
  try {
    const {
      simulationId,
      questionNumber,
      questionData,
      userAnswer = null
    } = req.body;

    // Validações
    if (!simulationId || !questionNumber || !questionData) {
      return res.status(400).json({
        error: 'Dados obrigatórios',
        message: 'simulationId, questionNumber e questionData são obrigatórios'
      });
    }

    // Verificar se o simulado pertence ao usuário
    const simulationResult = await query(
      'SELECT user_id FROM simulations WHERE id = $1',
      [simulationId]
    );

    if (simulationResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Simulado não encontrado',
        message: 'O simulado especificado não existe'
      });
    }

    if (simulationResult.rows[0].user_id !== req.user.id) {
      return res.status(403).json({
        error: 'Acesso negado',
        message: 'Você não tem permissão para acessar este simulado'
      });
    }

    // Verificar se a resposta está correta
    const isCorrect = userAnswer ? 
      checkAnswer(questionData.correct_answer, userAnswer) : null;

    // Salvar questão no banco
    const result = await query(
      `INSERT INTO simulation_questions (
        simulation_id, question_number, question_text, question_type,
        options, correct_answer, user_answer, is_correct,
        difficulty_level, content_area, rationale
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id`,
      [
        simulationId,
        questionNumber,
        questionData.question_text,
        questionData.question_type,
        JSON.stringify(questionData.options),
        JSON.stringify(questionData.correct_answer),
        userAnswer ? JSON.stringify(userAnswer) : null,
        isCorrect,
        questionData.difficulty_value || getDifficultyValue(questionData.difficulty_level),
        questionData.content_area,
        JSON.stringify(questionData.rationale)
      ]
    );

    res.json({
      message: 'Questão salva com sucesso',
      questionId: result.rows[0].id,
      isCorrect
    });

  } catch (error) {
    console.error('Erro ao salvar questão:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao salvar questão'
    });
  }
};

// Traduzir questão para português
const translateQuestion = async (req, res) => {
  try {
    const { questionText } = req.body;

    if (!questionText) {
      return res.status(400).json({
        error: 'Texto obrigatório',
        message: 'questionText é obrigatório'
      });
    }

    const translation = await llmService.translateQuestion(questionText);

    res.json({
      original: questionText,
      translation,
      translated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao traduzir questão:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao traduzir questão'
    });
  }
};

// Obter estatísticas de questões
const getQuestionStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await query(`
      SELECT 
        COUNT(*) as total_questions,
        COUNT(CASE WHEN is_correct = true THEN 1 END) as correct_answers,
        COUNT(CASE WHEN is_correct = false THEN 1 END) as incorrect_answers,
        AVG(CASE WHEN is_correct = true THEN 1.0 ELSE 0.0 END) * 100 as accuracy_percentage,
        COUNT(CASE WHEN was_translated = true THEN 1 END) as translations_used
      FROM simulation_questions sq
      JOIN simulations s ON sq.simulation_id = s.id
      WHERE s.user_id = $1
    `, [userId]);

    const contentAreaStats = await query(`
      SELECT 
        content_area,
        COUNT(*) as total,
        COUNT(CASE WHEN is_correct = true THEN 1 END) as correct,
        AVG(CASE WHEN is_correct = true THEN 1.0 ELSE 0.0 END) * 100 as accuracy
      FROM simulation_questions sq
      JOIN simulations s ON sq.simulation_id = s.id
      WHERE s.user_id = $1 AND sq.user_answer IS NOT NULL
      GROUP BY content_area
      ORDER BY total DESC
    `, [userId]);

    res.json({
      overall: stats.rows[0],
      by_content_area: contentAreaStats.rows
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao obter estatísticas'
    });
  }
};

// Funções auxiliares
function getDifficultyValue(difficulty) {
  const difficultyMap = {
    'easy': -1.0,
    'medium': 0.0,
    'hard': 1.0
  };
  return difficultyMap[difficulty] || 0.0;
}

function checkAnswer(correctAnswer, userAnswer) {
  // Normalizar respostas para comparação
  const correct = Array.isArray(correctAnswer) ? correctAnswer.sort() : [correctAnswer];
  const user = Array.isArray(userAnswer) ? userAnswer.sort() : [userAnswer];
  
  return JSON.stringify(correct) === JSON.stringify(user);
}

module.exports = {
  generateQuestion,
  saveQuestion,
  translateQuestion,
  getQuestionStats,
  CONTENT_AREAS,
  QUESTION_TYPES,
  DIFFICULTY_LEVELS
};

