const analysisService = require('../services/analysisService');

class AnalysisController {
  // Obter análise geral de performance do usuário
  async getUserPerformanceAnalysis(req, res) {
    try {
      const userId = req.user.id;
      
      const analysis = await analysisService.analyzeUserPerformance(userId);
      
      res.json({
        success: true,
        data: analysis
      });
    } catch (error) {
      console.error('Erro ao obter análise de performance:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }

  // Obter análise detalhada de um simulado específico
  async getSimulationAnalysis(req, res) {
    try {
      const { simulationId } = req.params;
      const userId = req.user.id;
      
      if (!simulationId) {
        return res.status(400).json({
          success: false,
          message: 'ID do simulado é obrigatório'
        });
      }

      const analysis = await analysisService.analyzeSimulationDetails(simulationId, userId);
      
      res.json({
        success: true,
        data: analysis
      });
    } catch (error) {
      console.error('Erro ao obter análise do simulado:', error);
      
      if (error.message === 'Simulado não encontrado') {
        return res.status(404).json({
          success: false,
          message: 'Simulado não encontrado'
        });
      }
      
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }

  // Obter histórico de simulados do usuário
  async getUserSimulationHistory(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 10, offset = 0 } = req.query;
      
      const db = require('../config/database');
      
      const query = `
        SELECT 
          s.id,
          s.status,
          s.total_questions,
          s.correct_answers,
          s.estimated_ability,
          s.confidence_level,
          s.created_at,
          s.completed_at,
          ROUND((s.correct_answers::float / s.total_questions * 100)::numeric, 2) as accuracy,
          EXTRACT(EPOCH FROM (s.completed_at - s.created_at))/60 as duration_minutes
        FROM simulations s
        WHERE s.user_id = $1
        ORDER BY s.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM simulations s
        WHERE s.user_id = $1
      `;
      
      const [historyResult, countResult] = await Promise.all([
        db.query(query, [userId, parseInt(limit), parseInt(offset)]),
        db.query(countQuery, [userId])
      ]);
      
      const simulations = historyResult.rows.map(sim => ({
        id: sim.id,
        status: sim.status,
        totalQuestions: sim.total_questions,
        correctAnswers: sim.correct_answers,
        accuracy: parseFloat(sim.accuracy),
        finalAbility: sim.estimated_ability ? Math.round(parseFloat(sim.estimated_ability) * 100) / 100 : null,
        confidenceLevel: sim.confidence_level,
        createdAt: sim.created_at,
        completedAt: sim.completed_at,
        durationMinutes: sim.duration_minutes ? Math.round(sim.duration_minutes * 100) / 100 : null
      }));
      
      res.json({
        success: true,
        data: {
          simulations,
          pagination: {
            total: parseInt(countResult.rows[0].total),
            limit: parseInt(limit),
            offset: parseInt(offset),
            hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].total)
          }
        }
      });
    } catch (error) {
      console.error('Erro ao obter histórico de simulados:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }

  // Obter estatísticas de áreas de conteúdo
  async getContentAreaStatistics(req, res) {
    try {
      const userId = req.user.id;
      
      const contentAreas = await analysisService.analyzeContentAreas(userId);
      
      res.json({
        success: true,
        data: {
          contentAreas,
          summary: {
            totalAreas: contentAreas.length,
            strongAreas: contentAreas.filter(area => area.strengthLevel === 'strong').length,
            weakAreas: contentAreas.filter(area => area.strengthLevel === 'weak' || area.strengthLevel === 'critical').length,
            averageAccuracy: contentAreas.length > 0 
              ? Math.round(contentAreas.reduce((sum, area) => sum + area.accuracy, 0) / contentAreas.length * 100) / 100
              : 0
          }
        }
      });
    } catch (error) {
      console.error('Erro ao obter estatísticas de áreas de conteúdo:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  },

  // Obter insights e recomendações personalizadas
  async getPersonalizedInsights(req, res) {
    try {
      const userId = req.user.id;
    
    const analysis = await analysisService.analyzeUserPerformance(userId);
    
    // Extrair apenas insights e recomendações
    const insights = {
      improvementTrend: analysis.improvementTrend,
      recommendations: analysis.recommendations,
      strengths: analysis.contentAreaAnalysis
        .filter(area => area.strengthLevel === 'strong')
        .map(area => area.contentArea),
      weaknesses: analysis.contentAreaAnalysis
        .filter(area => area.strengthLevel === 'weak' || area.strengthLevel === 'critical')
        .map(area => area.contentArea),
      overallLevel: this.determineOverallLevel(analysis.averageAccuracy, analysis.averageAbility),
      nextSteps: this.generateNextSteps(analysis)
    };
    
    res.json({
      success: true,
      data: insights
    });
    } catch (error) {
      console.error('Erro ao obter insights personalizados:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }

  // Determinar nível geral do usuário
  determineOverallLevel(accuracy, ability) {
    if (accuracy >= 85 && ability >= 1.5) return 'advanced';
    if (accuracy >= 75 && ability >= 1.0) return 'proficient';
    if (accuracy >= 65 && ability >= 0.5) return 'intermediate';
    if (accuracy >= 50 && ability >= 0) return 'developing';
    return 'beginner';
  }

  // Gerar próximos passos recomendados
  generateNextSteps(analysis) {
    const steps = [];
    
    if (analysis.totalSimulations < 3) {
      steps.push('Complete mais simulados para obter análises mais precisas');
    }
    
    if (analysis.averageAccuracy < 65) {
      steps.push('Foque em revisar conceitos fundamentais');
      steps.push('Pratique questões básicas diariamente');
    } else if (analysis.averageAccuracy < 80) {
      steps.push('Continue praticando regularmente');
      steps.push('Analise as justificativas das questões incorretas');
    } else {
      steps.push('Mantenha a prática consistente');
      steps.push('Desafie-se com questões mais complexas');
    }
    
    if (analysis.improvementTrend === 'declining') {
      steps.push('Revise sua estratégia de estudos');
      steps.push('Considere fazer pausas para evitar fadiga');
    }
    
    const weakAreas = analysis.contentAreaAnalysis
      .filter(area => area.strengthLevel === 'weak' || area.strengthLevel === 'critical')
      .slice(0, 2);
    
    if (weakAreas.length > 0) {
      steps.push(`Priorize estudos em: ${weakAreas.map(area => area.contentArea).join(' e ')}`);
    }
    
    return steps;
  }

  // Comparar performance com médias gerais (simulado)
  async getPerformanceComparison(req, res) {
    try {
      const userId = req.user.id;
      
      // Para demonstração, vamos usar dados simulados de comparação
      // Em produção, isso viria de análises agregadas de todos os usuários
      const userAnalysis = await analysisService.analyzeUserPerformance(userId);
      
      const comparison = {
        userStats: {
          averageAccuracy: userAnalysis.averageAccuracy,
          averageAbility: userAnalysis.averageAbility,
          totalSimulations: userAnalysis.totalSimulations
        },
        globalAverages: {
          averageAccuracy: 68.5, // Simulado
          averageAbility: 0.3,    // Simulado
          averageSimulations: 8.2  // Simulado
        },
        percentiles: {
          accuracy: this.calculatePercentile(userAnalysis.averageAccuracy, 68.5, 15),
          ability: this.calculatePercentile(userAnalysis.averageAbility, 0.3, 0.8),
          activity: this.calculatePercentile(userAnalysis.totalSimulations, 8.2, 5)
        },
        ranking: {
          overall: this.calculateOverallRanking(userAnalysis),
          category: this.determineOverallLevel(userAnalysis.averageAccuracy, userAnalysis.averageAbility)
        }
      };
      
      res.json({
        success: true,
        data: comparison
      });
    } catch (error) {
      console.error('Erro ao obter comparação de performance:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }

  // Calcular percentil aproximado
  calculatePercentile(userValue, average, stdDev) {
    const zScore = (userValue - average) / stdDev;
    // Aproximação simples de percentil baseada em z-score
    let percentile = 50 + (zScore * 20);
    return Math.max(1, Math.min(99, Math.round(percentile)));
  }

  // Calcular ranking geral
  calculateOverallRanking(analysis) {
    // Fórmula simples para ranking baseada em múltiplos fatores
    const accuracyScore = Math.min(analysis.averageAccuracy / 100, 1) * 40;
    const abilityScore = Math.max(0, Math.min((analysis.averageAbility + 2) / 4, 1)) * 30;
    const activityScore = Math.min(analysis.totalSimulations / 10, 1) * 20;
    const trendScore = analysis.improvementTrend === 'improving' ? 10 : 
                      analysis.improvementTrend === 'stable' ? 5 : 0;
    
    const totalScore = accuracyScore + abilityScore + activityScore + trendScore;
    return Math.round(totalScore);
  }
}

module.exports = new AnalysisController();

