const User = require('../models/User');
const { generateToken } = require('../middleware/auth');

// Validação de email
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validação de senha
const isValidPassword = (password) => {
  // Mínimo 8 caracteres, pelo menos 1 letra e 1 número
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
  return passwordRegex.test(password);
};

// Registro de novo usuário
const register = async (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;

    // Validações
    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({
        error: '<PERSON><PERSON> obrigatórios',
        message: '<PERSON><PERSON>, senha, nome e sobrenome são obrigatórios'
      });
    }

    if (!isValidEmail(email)) {
      return res.status(400).json({
        error: 'Email inválido',
        message: 'Por favor, forneça um email válido'
      });
    }

    if (!isValidPassword(password)) {
      return res.status(400).json({
        error: 'Senha inválida',
        message: 'A senha deve ter pelo menos 8 caracteres, incluindo letras e números'
      });
    }

    if (firstName.trim().length < 2 || lastName.trim().length < 2) {
      return res.status(400).json({
        error: 'Nome inválido',
        message: 'Nome e sobrenome devem ter pelo menos 2 caracteres'
      });
    }

    // Criar usuário
    const user = await User.create({
      email: email.toLowerCase().trim(),
      password,
      firstName: firstName.trim(),
      lastName: lastName.trim()
    });

    // Gerar token
    const token = generateToken(user.id);

    res.status(201).json({
      message: 'Usuário criado com sucesso',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Erro no registro:', error);
    
    if (error.message === 'Email já está em uso') {
      return res.status(409).json({
        error: 'Email já cadastrado',
        message: 'Este email já está sendo usado por outro usuário'
      });
    }

    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao criar usuário'
    });
  }
};

// Login de usuário
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validações
    if (!email || !password) {
      return res.status(400).json({
        error: 'Dados obrigatórios',
        message: 'Email e senha são obrigatórios'
      });
    }

    if (!isValidEmail(email)) {
      return res.status(400).json({
        error: 'Email inválido',
        message: 'Por favor, forneça um email válido'
      });
    }

    // Verificar credenciais
    const user = await User.verifyPassword(email.toLowerCase().trim(), password);

    if (!user) {
      return res.status(401).json({
        error: 'Credenciais inválidas',
        message: 'Email ou senha incorretos'
      });
    }

    // Gerar token
    const token = generateToken(user.id);

    res.json({
      message: 'Login realizado com sucesso',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Erro no login:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao realizar login'
    });
  }
};

// Obter perfil do usuário logado
const getProfile = async (req, res) => {
  try {
    // O usuário já está disponível através do middleware de autenticação
    res.json({
      user: req.user.toJSON()
    });
  } catch (error) {
    console.error('Erro ao obter perfil:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao obter perfil do usuário'
    });
  }
};

// Atualizar perfil do usuário
const updateProfile = async (req, res) => {
  try {
    const { firstName, lastName } = req.body;

    // Validações
    if (!firstName || !lastName) {
      return res.status(400).json({
        error: 'Dados obrigatórios',
        message: 'Nome e sobrenome são obrigatórios'
      });
    }

    if (firstName.trim().length < 2 || lastName.trim().length < 2) {
      return res.status(400).json({
        error: 'Nome inválido',
        message: 'Nome e sobrenome devem ter pelo menos 2 caracteres'
      });
    }

    // Atualizar perfil
    const updatedUser = await req.user.updateProfile({
      firstName: firstName.trim(),
      lastName: lastName.trim()
    });

    res.json({
      message: 'Perfil atualizado com sucesso',
      user: updatedUser.toJSON()
    });

  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao atualizar perfil'
    });
  }
};

// Logout (invalidar token - para futuras implementações com blacklist)
const logout = async (req, res) => {
  try {
    // Por enquanto, apenas retorna sucesso
    // Em uma implementação mais robusta, adicionaríamos o token a uma blacklist
    res.json({
      message: 'Logout realizado com sucesso'
    });
  } catch (error) {
    console.error('Erro no logout:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao realizar logout'
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  logout
};

