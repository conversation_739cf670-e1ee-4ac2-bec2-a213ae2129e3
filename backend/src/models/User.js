const { query } = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  constructor(userData) {
    this.id = userData.id;
    this.email = userData.email;
    this.firstName = userData.first_name;
    this.lastName = userData.last_name;
    this.isActive = userData.is_active;
    this.emailVerified = userData.email_verified;
    this.createdAt = userData.created_at;
    this.updatedAt = userData.updated_at;
    this.lastLogin = userData.last_login;
  }

  // Criar novo usuário
  static async create({ email, password, firstName, lastName }) {
    try {
      // Hash da senha
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      const result = await query(
        `INSERT INTO users (email, password_hash, first_name, last_name) 
         VALUES ($1, $2, $3, $4) 
         RETURNING id, email, first_name, last_name, is_active, email_verified, created_at, updated_at`,
        [email, passwordHash, firstName, lastName]
      );

      return new User(result.rows[0]);
    } catch (error) {
      if (error.code === '23505') { // Violação de unique constraint
        throw new Error('Email já está em uso');
      }
      throw error;
    }
  }

  // Buscar usuário por email
  static async findByEmail(email) {
    try {
      const result = await query(
        'SELECT * FROM users WHERE email = $1 AND is_active = true',
        [email]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Buscar usuário por ID
  static async findById(id) {
    try {
      const result = await query(
        'SELECT * FROM users WHERE id = $1 AND is_active = true',
        [id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return new User(result.rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Verificar senha
  static async verifyPassword(email, password) {
    try {
      const result = await query(
        'SELECT id, email, password_hash, first_name, last_name, is_active, email_verified, created_at, updated_at FROM users WHERE email = $1 AND is_active = true',
        [email]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      const isValidPassword = await bcrypt.compare(password, user.password_hash);

      if (!isValidPassword) {
        return null;
      }

      // Atualizar último login
      await query(
        'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
        [user.id]
      );

      return new User(user);
    } catch (error) {
      throw error;
    }
  }

  // Atualizar perfil do usuário
  async updateProfile({ firstName, lastName }) {
    try {
      const result = await query(
        `UPDATE users 
         SET first_name = $1, last_name = $2, updated_at = CURRENT_TIMESTAMP 
         WHERE id = $3 
         RETURNING id, email, first_name, last_name, is_active, email_verified, created_at, updated_at`,
        [firstName, lastName, this.id]
      );

      if (result.rows.length === 0) {
        throw new Error('Usuário não encontrado');
      }

      // Atualizar propriedades da instância
      const updatedUser = result.rows[0];
      this.firstName = updatedUser.first_name;
      this.lastName = updatedUser.last_name;
      this.updatedAt = updatedUser.updated_at;

      return this;
    } catch (error) {
      throw error;
    }
  }

  // Desativar usuário (soft delete)
  async deactivate() {
    try {
      await query(
        'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [this.id]
      );

      this.isActive = false;
      return this;
    } catch (error) {
      throw error;
    }
  }

  // Converter para JSON (remover dados sensíveis)
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      fullName: `${this.firstName} ${this.lastName}`,
      isActive: this.isActive,
      emailVerified: this.emailVerified,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      lastLogin: this.lastLogin
    };
  }
}

module.exports = User;

