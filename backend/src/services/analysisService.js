const db = require('../config/database');

class AnalysisService {
  // Analisar desempenho geral do usuário
  async analyzeUserPerformance(userId) {
    try {
      const query = `
        SELECT 
          s.id,
          s.status,
          s.total_questions,
          s.correct_answers,
          s.estimated_ability,
          s.confidence_level,
          s.created_at,
          s.completed_at,
          EXTRACT(EPOCH FROM (s.completed_at - s.started_at))/60 as duration_minutes
        FROM simulations s
        WHERE s.user_id = $1 AND s.status = 'completed'
        ORDER BY s.created_at DESC
      `;
      
      const result = await db.query(query, [userId]);
      const simulations = result.rows;

      if (simulations.length === 0) {
        return {
          totalSimulations: 0,
          averageAccuracy: 0,
          averageAbility: 0,
          improvementTrend: 'insufficient_data',
          recommendations: ['Complete pelo menos um simulado para ver análises detalhadas.']
        };
      }

      // Calcular estatísticas gerais
      const totalSimulations = simulations.length;
      const averageAccuracy = simulations.reduce((sum, sim) => 
        sum + (sim.correct_answers / sim.total_questions * 100), 0) / totalSimulations;
      const averageAbility = simulations.reduce((sum, sim) => 
        sum + parseFloat(sim.estimated_ability), 0) / totalSimulations;
      const averageConfidence = simulations.reduce((sum, sim) => 
        sum + sim.confidence_level, 0) / totalSimulations;

      // Analisar tendência de melhoria
      const improvementTrend = this.calculateImprovementTrend(simulations);
      
      // Analisar áreas de conteúdo
      const contentAreaAnalysis = await this.analyzeContentAreas(userId);
      
      // Gerar recomendações personalizadas
      const recommendations = this.generateRecommendations(
        averageAccuracy, 
        averageAbility, 
        improvementTrend, 
        contentAreaAnalysis
      );

      return {
        totalSimulations,
        averageAccuracy: Math.round(averageAccuracy * 100) / 100,
        averageAbility: Math.round(averageAbility * 100) / 100,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        improvementTrend,
        contentAreaAnalysis,
        recommendations,
        recentSimulations: simulations.slice(0, 5),
        performanceHistory: this.generatePerformanceHistory(simulations)
      };
    } catch (error) {
      console.error('Erro ao analisar desempenho do usuário:', error);
      throw new Error('Erro interno do servidor');
    }
  }

  // Analisar áreas de conteúdo específicas
  async analyzeContentAreas(userId) {
    try {
      const query = `
        SELECT 
          sq.content_area,
          COUNT(*) as total_questions,
          SUM(CASE WHEN sq.is_correct THEN 1 ELSE 0 END) as correct_answers,
          AVG(sq.time_spent) as avg_time_spent,
          AVG(sq.difficulty_level) as avg_difficulty
        FROM simulation_questions sq
        JOIN simulations s ON sq.simulation_id = s.id
        WHERE s.user_id = $1 AND s.status = 'completed'
        GROUP BY sq.content_area
        ORDER BY total_questions DESC
      `;
      
      const result = await db.query(query, [userId]);
      
      return result.rows.map(row => ({
        contentArea: row.content_area,
        totalQuestions: parseInt(row.total_questions),
        correctAnswers: parseInt(row.correct_answers),
        accuracy: Math.round((row.correct_answers / row.total_questions) * 100 * 100) / 100,
        averageTimeSpent: Math.round(parseFloat(row.avg_time_spent) * 100) / 100,
        averageDifficulty: Math.round(parseFloat(row.avg_difficulty) * 100) / 100,
        strengthLevel: this.categorizeStrength(row.correct_answers / row.total_questions)
      }));
    } catch (error) {
      console.error('Erro ao analisar áreas de conteúdo:', error);
      return [];
    }
  }

  // Calcular tendência de melhoria
  calculateImprovementTrend(simulations) {
    if (simulations.length < 2) return 'insufficient_data';
    
    // Pegar os últimos 5 simulados para análise de tendência
    const recentSims = simulations.slice(0, Math.min(5, simulations.length));
    const abilities = recentSims.map(sim => parseFloat(sim.estimated_ability));
    
    // Calcular tendência linear simples
    let trend = 0;
    for (let i = 1; i < abilities.length; i++) {
      trend += abilities[i-1] - abilities[i]; // Ordem reversa porque está DESC
    }
    
    if (trend > 0.2) return 'improving';
    if (trend < -0.2) return 'declining';
    return 'stable';
  }

  // Categorizar força em área de conteúdo
  categorizeStrength(accuracy) {
    if (accuracy >= 0.8) return 'strong';
    if (accuracy >= 0.65) return 'moderate';
    if (accuracy >= 0.5) return 'weak';
    return 'critical';
  }

  // Gerar recomendações personalizadas
  generateRecommendations(averageAccuracy, averageAbility, trend, contentAreas) {
    const recommendations = [];
    
    // Recomendações baseadas na precisão geral
    if (averageAccuracy < 50) {
      recommendations.push('Foque em revisar conceitos fundamentais de enfermagem');
      recommendations.push('Pratique questões básicas antes de tentar simulados completos');
    } else if (averageAccuracy < 65) {
      recommendations.push('Continue praticando regularmente para melhorar a consistência');
      recommendations.push('Revise as justificativas das questões incorretas');
    } else if (averageAccuracy < 80) {
      recommendations.push('Você está no caminho certo! Foque nas áreas mais fracas');
      recommendations.push('Pratique questões de nível mais avançado');
    } else {
      recommendations.push('Excelente desempenho! Mantenha a prática regular');
      recommendations.push('Foque em manter a consistência em todas as áreas');
    }

    // Recomendações baseadas na tendência
    if (trend === 'declining') {
      recommendations.push('Sua performance recente está em declínio. Considere revisar estratégias de estudo');
      recommendations.push('Faça pausas regulares para evitar fadiga mental');
    } else if (trend === 'improving') {
      recommendations.push('Parabéns! Sua performance está melhorando consistentemente');
      recommendations.push('Continue com a estratégia atual de estudos');
    }

    // Recomendações baseadas em áreas de conteúdo
    const weakAreas = contentAreas.filter(area => area.strengthLevel === 'critical' || area.strengthLevel === 'weak');
    if (weakAreas.length > 0) {
      recommendations.push(`Priorize estudos em: ${weakAreas.map(area => area.contentArea).join(', ')}`);
    }

    const strongAreas = contentAreas.filter(area => area.strengthLevel === 'strong');
    if (strongAreas.length > 0) {
      recommendations.push(`Mantenha o bom trabalho em: ${strongAreas.map(area => area.contentArea).join(', ')}`);
    }

    return recommendations;
  }

  // Gerar histórico de performance para gráficos
  generatePerformanceHistory(simulations) {
    return simulations.reverse().map((sim, index) => ({
      simulationNumber: index + 1,
      date: sim.created_at,
      accuracy: Math.round((sim.correct_answers / sim.total_questions) * 100 * 100) / 100,
      ability: Math.round(parseFloat(sim.estimated_ability) * 100) / 100,
      confidence: sim.confidence_level,
      totalQuestions: sim.total_questions,
      duration: sim.duration_minutes ? Math.round(sim.duration_minutes * 100) / 100 : null
    }));
  }

  // Analisar simulado específico em detalhes
  async analyzeSimulationDetails(simulationId, userId) {
    try {
      // Verificar se o simulado pertence ao usuário
      const simQuery = `
        SELECT * FROM simulations 
        WHERE id = $1 AND user_id = $2
      `;
      const simResult = await db.query(simQuery, [simulationId, userId]);
      
      if (simResult.rows.length === 0) {
        throw new Error('Simulado não encontrado');
      }

      const simulation = simResult.rows[0];

      // Buscar todas as questões do simulado
      const questionsQuery = `
        SELECT 
          sq.*,
          CASE 
            WHEN sq.is_correct THEN 'correct'
            ELSE 'incorrect'
          END as result_type
        FROM simulation_questions sq
        WHERE sq.simulation_id = $1
        ORDER BY sq.question_order
      `;
      
      const questionsResult = await db.query(questionsQuery, [simulationId]);
      const questions = questionsResult.rows;

      // Analisar padrões de resposta
      const responsePatterns = this.analyzeResponsePatterns(questions);
      
      // Analisar distribuição de dificuldade
      const difficultyAnalysis = this.analyzeDifficultyDistribution(questions);
      
      // Analisar tempo por questão
      const timeAnalysis = this.analyzeTimeSpent(questions);

      return {
        simulation: {
          id: simulation.id,
          totalQuestions: simulation.total_questions,
          correctAnswers: simulation.correct_answers,
          accuracy: Math.round((simulation.correct_answers / simulation.total_questions) * 100 * 100) / 100,
          finalAbility: Math.round(parseFloat(simulation.final_ability) * 100) / 100,
          confidenceLevel: simulation.confidence_level,
          status: simulation.status,
          createdAt: simulation.created_at,
          completedAt: simulation.completed_at
        },
        questions: questions.map(q => ({
          order: q.question_order,
          contentArea: q.content_area,
          difficulty: Math.round(q.difficulty * 100) / 100,
          isCorrect: q.is_correct,
          timeSpent: Math.round(q.time_spent * 100) / 100,
          abilityBefore: Math.round(q.ability_before * 100) / 100,
          abilityAfter: Math.round(q.ability_after * 100) / 100
        })),
        responsePatterns,
        difficultyAnalysis,
        timeAnalysis,
        insights: this.generateSimulationInsights(simulation, questions, responsePatterns, timeAnalysis)
      };
    } catch (error) {
      console.error('Erro ao analisar detalhes do simulado:', error);
      throw error;
    }
  }

  // Analisar padrões de resposta
  analyzeResponsePatterns(questions) {
    const totalQuestions = questions.length;
    const correctAnswers = questions.filter(q => q.is_correct).length;
    
    // Analisar sequências de acertos/erros
    let currentStreak = 0;
    let longestCorrectStreak = 0;
    let longestIncorrectStreak = 0;
    let currentStreakType = null;
    
    questions.forEach(q => {
      if (q.is_correct) {
        if (currentStreakType === 'correct') {
          currentStreak++;
        } else {
          currentStreak = 1;
          currentStreakType = 'correct';
        }
        longestCorrectStreak = Math.max(longestCorrectStreak, currentStreak);
      } else {
        if (currentStreakType === 'incorrect') {
          currentStreak++;
        } else {
          currentStreak = 1;
          currentStreakType = 'incorrect';
        }
        longestIncorrectStreak = Math.max(longestIncorrectStreak, currentStreak);
      }
    });

    return {
      totalQuestions,
      correctAnswers,
      incorrectAnswers: totalQuestions - correctAnswers,
      accuracy: Math.round((correctAnswers / totalQuestions) * 100 * 100) / 100,
      longestCorrectStreak,
      longestIncorrectStreak,
      consistency: this.calculateConsistency(questions)
    };
  }

  // Calcular consistência de performance
  calculateConsistency(questions) {
    if (questions.length < 5) return 'insufficient_data';
    
    // Dividir em blocos de 5 questões e calcular variação
    const blockSize = 5;
    const blocks = [];
    
    for (let i = 0; i < questions.length; i += blockSize) {
      const block = questions.slice(i, i + blockSize);
      const accuracy = block.filter(q => q.is_correct).length / block.length;
      blocks.push(accuracy);
    }
    
    if (blocks.length < 2) return 'insufficient_data';
    
    // Calcular desvio padrão
    const mean = blocks.reduce((sum, acc) => sum + acc, 0) / blocks.length;
    const variance = blocks.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / blocks.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev < 0.15) return 'high';
    if (stdDev < 0.25) return 'moderate';
    return 'low';
  }

  // Analisar distribuição de dificuldade
  analyzeDifficultyDistribution(questions) {
    const difficulties = questions.map(q => q.difficulty);
    const avgDifficulty = difficulties.reduce((sum, diff) => sum + diff, 0) / difficulties.length;
    
    const difficultyRanges = {
      easy: questions.filter(q => q.difficulty < -0.5).length,
      moderate: questions.filter(q => q.difficulty >= -0.5 && q.difficulty <= 0.5).length,
      hard: questions.filter(q => q.difficulty > 0.5).length
    };

    return {
      averageDifficulty: Math.round(avgDifficulty * 100) / 100,
      distribution: difficultyRanges,
      adaptationEffectiveness: this.calculateAdaptationEffectiveness(questions)
    };
  }

  // Calcular efetividade da adaptação TRI
  calculateAdaptationEffectiveness(questions) {
    // Verificar se a dificuldade se adaptou adequadamente à habilidade
    let adaptationScore = 0;
    
    questions.forEach(q => {
      const difficultyGap = Math.abs(q.difficulty - q.ability_before);
      if (difficultyGap < 0.5) adaptationScore += 1; // Boa adaptação
      else if (difficultyGap < 1.0) adaptationScore += 0.5; // Adaptação moderada
    });
    
    const effectiveness = adaptationScore / questions.length;
    
    if (effectiveness > 0.8) return 'excellent';
    if (effectiveness > 0.6) return 'good';
    if (effectiveness > 0.4) return 'moderate';
    return 'poor';
  }

  // Analisar tempo gasto
  analyzeTimeSpent(questions) {
    const times = questions.map(q => q.time_spent);
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const totalTime = times.reduce((sum, time) => sum + time, 0);
    
    return {
      averageTimePerQuestion: Math.round(avgTime * 100) / 100,
      totalTimeSpent: Math.round(totalTime * 100) / 100,
      fastestQuestion: Math.min(...times),
      slowestQuestion: Math.max(...times),
      timeConsistency: this.calculateTimeConsistency(times)
    };
  }

  // Calcular consistência de tempo
  calculateTimeConsistency(times) {
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const stdDev = Math.sqrt(variance);
    const coefficient = stdDev / mean;
    
    if (coefficient < 0.3) return 'high';
    if (coefficient < 0.5) return 'moderate';
    return 'low';
  }

  // Gerar insights do simulado
  generateSimulationInsights(simulation, questions, patterns, timeAnalysis) {
    const insights = [];
    
    // Insights sobre precisão
    if (patterns.accuracy >= 80) {
      insights.push({
        type: 'positive',
        category: 'performance',
        message: 'Excelente precisão! Você demonstrou domínio sólido do conteúdo.'
      });
    } else if (patterns.accuracy < 60) {
      insights.push({
        type: 'warning',
        category: 'performance',
        message: 'Precisão abaixo do esperado. Considere revisar os conceitos fundamentais.'
      });
    }

    // Insights sobre consistência
    if (patterns.consistency === 'low') {
      insights.push({
        type: 'warning',
        category: 'consistency',
        message: 'Performance inconsistente. Trabalhe na estabilidade das respostas.'
      });
    } else if (patterns.consistency === 'high') {
      insights.push({
        type: 'positive',
        category: 'consistency',
        message: 'Performance muito consistente ao longo do simulado.'
      });
    }

    // Insights sobre tempo
    if (timeAnalysis.averageTimePerQuestion < 60) {
      insights.push({
        type: 'warning',
        category: 'time',
        message: 'Você está respondendo muito rapidamente. Considere ler as questões com mais atenção.'
      });
    } else if (timeAnalysis.averageTimePerQuestion > 180) {
      insights.push({
        type: 'info',
        category: 'time',
        message: 'Tempo elevado por questão. Pratique estratégias de gerenciamento de tempo.'
      });
    }

    // Insights sobre sequências
    if (patterns.longestIncorrectStreak >= 5) {
      insights.push({
        type: 'warning',
        category: 'pattern',
        message: `Sequência de ${patterns.longestIncorrectStreak} erros consecutivos detectada. Mantenha a calma e foque.`
      });
    }

    if (patterns.longestCorrectStreak >= 8) {
      insights.push({
        type: 'positive',
        category: 'pattern',
        message: `Excelente! Sequência de ${patterns.longestCorrectStreak} acertos consecutivos.`
      });
    }

    return insights;
  }
}

module.exports = new AnalysisService();

