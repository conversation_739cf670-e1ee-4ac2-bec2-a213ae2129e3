// Serviço mock para simular geração de questões LLM durante desenvolvimento
class MockLLMService {
  constructor() {
    this.usedQuestions = new Set(); // Controle de questões já usadas
    this.mockQuestions = [
      {
        question_text: "A nurse is preparing to administer medication to a client. Which action should the nurse take first to ensure client safety?",
        question_type: "multiple_choice",
        content_area: "Safe and Effective Care Environment",
        difficulty_level: "easy",
        options: [
          {"id": "A", "text": "Check the client's identification band"},
          {"id": "B", "text": "Prepare the medication in the medication room"},
          {"id": "C", "text": "Document the medication administration"},
          {"id": "D", "text": "Assess the client's vital signs"}
        ],
        correct_answer: ["A"],
        rationale: {
          correct: "Checking the client's identification band is the first step in the 'Five Rights' of medication administration.",
          incorrect: {
            "B": "While medication preparation is important, client identification must come first.",
            "C": "Documentation occurs after medication administration, not before.",
            "D": "Vital signs assessment may be needed but client identification is the priority."
          }
        }
      },
      {
        question_text: "A client with diabetes mellitus is admitted with diabetic ketoacidosis (DKA). The nurse should monitor for which priority complication?",
        question_type: "multiple_choice",
        content_area: "Safe and Effective Care Environment",
        difficulty_level: "medium",
        options: [
          {"id": "A", "text": "Hyperglycemia"},
          {"id": "B", "text": "Dehydration and electrolyte imbalance"},
          {"id": "C", "text": "Weight gain"},
          {"id": "D", "text": "Bradycardia"}
        ],
        correct_answer: ["B"],
        rationale: {
          correct: "Dehydration and electrolyte imbalances are life-threatening complications of DKA.",
          incorrect: {
            "A": "Hyperglycemia is already present in DKA.",
            "C": "Weight loss, not gain, occurs in DKA.",
            "D": "Tachycardia, not bradycardia, is typically seen."
          }
        }
      },
      {
        question_text: "A client is receiving oxygen therapy via nasal cannula at 2 L/min. Which sign indicates adequate oxygenation?",
        question_type: "multiple_choice",
        content_area: "Physiological Integrity",
        difficulty_level: "easy",
        options: [
          {"id": "A", "text": "Pink nail beds"},
          {"id": "B", "text": "Cyanosis around the lips"},
          {"id": "C", "text": "Restlessness and confusion"},
          {"id": "D", "text": "Respiratory rate of 28/min"}
        ],
        correct_answer: ["A"],
        rationale: {
          correct: "Pink nail beds indicate adequate tissue perfusion and oxygenation.",
          incorrect: {
            "B": "Cyanosis indicates poor oxygenation.",
            "C": "Restlessness and confusion are signs of hypoxia.",
            "D": "Tachypnea suggests respiratory distress."
          }
        }
      },
      {
        question_text: "A postoperative client reports pain level 8/10. What is the nurse's priority action?",
        question_type: "multiple_choice",
        content_area: "Physiological Integrity",
        difficulty_level: "medium",
        options: [
          {"id": "A", "text": "Administer prescribed pain medication"},
          {"id": "B", "text": "Encourage deep breathing exercises"},
          {"id": "C", "text": "Reposition the client"},
          {"id": "D", "text": "Document the pain assessment"}
        ],
        correct_answer: ["A"],
        rationale: {
          correct: "Pain level 8/10 requires immediate pharmacological intervention.",
          incorrect: {
            "B": "Deep breathing is helpful but won't address severe pain.",
            "C": "Repositioning may help but medication is priority for severe pain.",
            "D": "Documentation is important but pain relief is the priority."
          }
        }
      },
      {
        question_text: "A client with heart failure is prescribed furosemide (Lasix). Which assessment is most important before administration?",
        question_type: "multiple_choice",
        content_area: "Pharmacological Therapies",
        difficulty_level: "medium",
        options: [
          {"id": "A", "text": "Blood pressure"},
          {"id": "B", "text": "Potassium level"},
          {"id": "C", "text": "Heart rate"},
          {"id": "D", "text": "Oxygen saturation"}
        ],
        correct_answer: ["B"],
        rationale: {
          correct: "Furosemide can cause hypokalemia, which can lead to dangerous arrhythmias.",
          incorrect: {
            "A": "Blood pressure is important but potassium level is priority.",
            "C": "Heart rate should be monitored but potassium is more critical.",
            "D": "Oxygen saturation is relevant but not the priority assessment."
          }
        }
      },
      {
        question_text: "A nurse is caring for a client with pneumonia. Which intervention should be prioritized?",
        question_type: "multiple_choice",
        content_area: "Physiological Integrity",
        difficulty_level: "easy",
        options: [
          {"id": "A", "text": "Encourage fluid intake"},
          {"id": "B", "text": "Administer oxygen as prescribed"},
          {"id": "C", "text": "Assist with ambulation"},
          {"id": "D", "text": "Provide nutritional support"}
        ],
        correct_answer: ["B"],
        rationale: {
          correct: "Oxygen therapy is priority to maintain adequate oxygenation in pneumonia.",
          incorrect: {
            "A": "Fluids are important but oxygenation is the priority.",
            "C": "Ambulation helps prevent complications but oxygen comes first.",
            "D": "Nutrition is important for recovery but not the immediate priority."
          }
        }
      },
      {
        question_text: "A client is scheduled for surgery and asks about the informed consent. What should the nurse explain?",
        question_type: "multiple_choice",
        content_area: "Safe and Effective Care Environment",
        difficulty_level: "easy",
        options: [
          {"id": "A", "text": "The surgeon will explain all risks and benefits"},
          {"id": "B", "text": "The nurse can answer all questions about the procedure"},
          {"id": "C", "text": "Consent can be signed by a family member"},
          {"id": "D", "text": "The consent form is just a legal formality"}
        ],
        correct_answer: ["A"],
        rationale: {
          correct: "The surgeon is responsible for explaining the procedure, risks, and benefits.",
          incorrect: {
            "B": "Nurses can provide general information but detailed explanation is surgeon's role.",
            "C": "Only the client can sign unless legally incompetent.",
            "D": "Informed consent is a legal and ethical requirement, not just formality."
          }
        }
      },
      {
        question_text: "A client with hypertension is prescribed lisinopril. Which side effect should the nurse monitor for?",
        question_type: "multiple_choice",
        content_area: "Pharmacological Therapies",
        difficulty_level: "easy",
        options: [
          {"id": "A", "text": "Dry cough"},
          {"id": "B", "text": "Weight gain"},
          {"id": "C", "text": "Increased appetite"},
          {"id": "D", "text": "Hyperglycemia"}
        ],
        correct_answer: ["A"],
        rationale: {
          correct: "Dry cough is a common side effect of ACE inhibitors like lisinopril.",
          incorrect: {
            "B": "Weight loss, not gain, may occur with ACE inhibitors.",
            "C": "Decreased appetite is more likely than increased.",
            "D": "ACE inhibitors don't typically cause hyperglycemia."
          }
        }
      }
    ];
  }

  async generateQuestion(options = {}) {
    const {
      difficulty = 'medium',
      contentArea = 'Safe and Effective Care Environment',
      questionType = 'multiple_choice',
      usedQuestions = []
    } = options;

    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Filtrar questões disponíveis (não usadas)
    const availableQuestions = this.mockQuestions.filter(q => 
      !usedQuestions.includes(q.question_text)
    );

    if (availableQuestions.length === 0) {
      // Se todas as questões foram usadas, resetar e usar qualquer uma
      console.log('🔄 Todas as questões foram usadas, resetando pool');
      const randomQuestion = this.mockQuestions[Math.floor(Math.random() * this.mockQuestions.length)];
      return {
        ...randomQuestion,
        generated_at: new Date().toISOString(),
        llm_model: 'mock-service',
        note: 'Questão reciclada - todas as questões do pool foram usadas'
      };
    }

    // Tentar encontrar questão que corresponda aos critérios
    let matchingQuestions = availableQuestions.filter(q => 
      q.content_area === contentArea && q.difficulty_level === difficulty
    );

    // Se não encontrar questão exata, relaxar critérios
    if (matchingQuestions.length === 0) {
      matchingQuestions = availableQuestions.filter(q => q.content_area === contentArea);
    }

    // Se ainda não encontrar, usar qualquer questão disponível
    if (matchingQuestions.length === 0) {
      matchingQuestions = availableQuestions;
    }

    // Selecionar questão aleatória do pool filtrado
    const selectedQuestion = matchingQuestions[Math.floor(Math.random() * matchingQuestions.length)];

    return {
      ...selectedQuestion,
      generated_at: new Date().toISOString(),
      llm_model: 'mock-service',
      note: `Questão selecionada aleatoriamente do pool de ${matchingQuestions.length} questões disponíveis`
    };
  }

  async translateQuestion(questionText) {
    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 500));

    // Traduções mock simples
    const translations = {
      "A nurse is preparing to administer medication to a client. Which action should the nurse take first to ensure client safety?": 
        "Uma enfermeira está se preparando para administrar medicação a um cliente. Qual ação a enfermeira deve tomar primeiro para garantir a segurança do cliente?",
      
      "A client with diabetes mellitus is admitted to the hospital with diabetic ketoacidosis (DKA). The nurse should monitor for which priority complication?":
        "Um cliente com diabetes mellitus é admitido no hospital com cetoacidose diabética (CAD). A enfermeira deve monitorar qual complicação prioritária?",
      
      "A client is receiving oxygen therapy via nasal cannula at 2 L/min. The nurse should assess for which sign of adequate oxygenation?":
        "Um cliente está recebendo oxigenoterapia via cânula nasal a 2 L/min. A enfermeira deve avaliar qual sinal de oxigenação adequada?"
    };

    return translations[questionText] || `[TRADUÇÃO MOCK] ${questionText}`;
  }

  async generateRationale(questionText, correctAnswer, incorrectAnswers) {
    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 800));

    return {
      detailed_rationale: "Esta é uma justificativa detalhada gerada pelo serviço mock. Em um ambiente de produção, esta seria uma explicação completa baseada em evidências científicas e guidelines de enfermagem.",
      key_concepts: ["Segurança do paciente", "Processo de enfermagem", "Cuidados baseados em evidência"],
      clinical_implications: "As implicações clínicas incluem a importância de seguir protocolos estabelecidos e manter sempre o foco na segurança do paciente.",
      references: ["NANDA-I Nursing Diagnoses", "ANA Standards of Practice", "Evidence-Based Nursing Guidelines"]
    };
  }
}

module.exports = new MockLLMService();

