const express = require('express');
const router = express.Router();
// const analysisController = require('../controllers/analysisController');
const { authenticateToken } = require('../middleware/auth');

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// ROTAS TEMPORARIAMENTE DESABILITADAS PARA CORREÇÃO
// Rota para obter análise geral de performance do usuário
// router.get('/performance', analysisController.getUserPerformanceAnalysis);

// Rota para obter análise detalhada de um simulado específico
// router.get('/simulation/:simulationId', analysisController.getSimulationAnalysis);

// Rota para obter histórico de simulados
// router.get('/history', analysisController.getUserSimulationHistory);

// Rota para obter estatísticas de áreas de conteúdo
// router.get('/content-areas', analysisController.getContentAreaStatistics);

// Rota para obter insights e recomendações personalizadas
// router.get('/insights', analysisController.getPersonalizedInsights);

// Rota para comparar performance com médias gerais
// router.get('/comparison', analysisController.getPerformanceComparison);

// Rota temporária de status
router.get('/status', (req, res) => {
  res.json({
    success: true,
    message: 'Análises temporariamente desabilitadas para manutenção',
    data: {
      status: 'maintenance',
      availableFeatures: ['login', 'simulados', 'questões']
    }
  });
});

module.exports = router;

