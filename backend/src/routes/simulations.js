const express = require('express');
const router = express.Router();
const simulationController = require('../controllers/simulationController');
const { authenticateToken } = require('../middleware/auth');

// Todas as rotas de simulados requerem autenticação
router.use(authenticateToken);

// Iniciar novo simulado
router.post('/start', simulationController.startSimulation);

// Obter simulado em andamento
router.get('/current', simulationController.getCurrentSimulation);

// Obter próxima questão do simulado
router.get('/:simulationId/next-question', simulationController.getNextQuestion);

// Submeter resposta para questão
router.post('/:simulationId/questions/:questionId/answer', simulationController.submitAnswer);

// Obter histórico de simulados do usuário
router.get('/history', simulationController.getSimulationHistory);

// Obter detalhes de um simulado específico
router.get('/:simulationId', simulationController.getSimulationDetails);

module.exports = router;

